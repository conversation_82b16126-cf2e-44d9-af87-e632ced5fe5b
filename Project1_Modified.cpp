// Project1.cpp : 定义应用程序的入口点。
//

#include "pch.h"
#include "framework.h"
#include "Project1.h"

#define MAX_LOADSTRING 100

// 控件 ID 定义
#define ID_BUTTON1      1001
#define ID_BUTTON2      1002
#define ID_EDIT1        1003
#define ID_EDIT2        1004
#define ID_STATIC1      1005
#define ID_LISTBOX1     1006

// 全局变量:
HINSTANCE hInst;                                // 当前实例
WCHAR szTitle[MAX_LOADSTRING];                  // 标题栏文本
WCHAR szWindowClass[MAX_LOADSTRING];            // 主窗口类名

// 控件句柄
HWND hButton1, hButton2;
HWND hEdit1, hEdit2;
HWND hStatic1;
HWND hListBox1;

// 此代码模块中包含的函数的前向声明:
ATOM                MyRegisterClass(HINSTANCE hInstance);
BOOL                InitInstance(HINSTANCE, int);
LRESULT CALLBACK    WndProc(HWND, UINT, WPARAM, LPARAM);
INT_PTR CALLBACK    About(HWND, UINT, WPARAM, LPARAM);

int APIENTRY wWinMain(_In_ HINSTANCE hInstance,
                     _In_opt_ HINSTANCE hPrevInstance,
                     _In_ LPWSTR    lpCmdLine,
                     _In_ int       nCmdShow)
{
    UNREFERENCED_PARAMETER(hPrevInstance);
    UNREFERENCED_PARAMETER(lpCmdLine);

    // TODO: 在此处放置代码。

    // 初始化全局字符串
    LoadStringW(hInstance, IDS_APP_TITLE, szTitle, MAX_LOADSTRING);
    LoadStringW(hInstance, IDC_PROJECT1, szWindowClass, MAX_LOADSTRING);
    MyRegisterClass(hInstance);

    // 执行应用程序初始化:
    if (!InitInstance (hInstance, nCmdShow))
    {
        return FALSE;
    }

    HACCEL hAccelTable = LoadAccelerators(hInstance, MAKEINTRESOURCE(IDC_PROJECT1));

    MSG msg;

    // 主消息循环:
    while (GetMessage(&msg, nullptr, 0, 0))
    {
        if (!TranslateAccelerator(msg.hwnd, hAccelTable, &msg))
        {
            TranslateMessage(&msg);
            DispatchMessage(&msg);
        }
    }

    return (int) msg.wParam;
}

//
//  函数: MyRegisterClass()
//
//  目标: 注册窗口类。
//
ATOM MyRegisterClass(HINSTANCE hInstance)
{
    WNDCLASSEXW wcex;

    wcex.cbSize = sizeof(WNDCLASSEX);

    wcex.style          = CS_HREDRAW | CS_VREDRAW;
    wcex.lpfnWndProc    = WndProc;
    wcex.cbClsExtra     = 0;
    wcex.cbWndExtra     = 0;
    wcex.hInstance      = hInstance;
    wcex.hIcon          = LoadIcon(hInstance, MAKEINTRESOURCE(IDI_PROJECT1));
    wcex.hCursor        = LoadCursor(nullptr, IDC_ARROW);
    wcex.hbrBackground  = (HBRUSH)(COLOR_WINDOW+1);
    wcex.lpszMenuName   = MAKEINTRESOURCEW(IDC_PROJECT1);
    wcex.lpszClassName  = szWindowClass;
    wcex.hIconSm        = LoadIcon(wcex.hInstance, MAKEINTRESOURCE(IDI_SMALL));

    return RegisterClassExW(&wcex);
}

//
//   函数: InitInstance(HINSTANCE, int)
//
//   目标: 保存实例句柄并创建主窗口
//
BOOL InitInstance(HINSTANCE hInstance, int nCmdShow)
{
   hInst = hInstance; // 将实例句柄存储在全局变量中

   HWND hWnd = CreateWindowW(szWindowClass, szTitle, WS_OVERLAPPEDWINDOW,
      CW_USEDEFAULT, 0, 800, 600, nullptr, nullptr, hInstance, nullptr);

   if (!hWnd)
   {
      return FALSE;
   }

   ShowWindow(hWnd, nCmdShow);
   UpdateWindow(hWnd);

   return TRUE;
}

//
//  函数: WndProc(HWND, UINT, WPARAM, LPARAM)
//
//  目标: 处理主窗口的消息。
//
LRESULT CALLBACK WndProc(HWND hWnd, UINT message, WPARAM wParam, LPARAM lParam)
{
    switch (message)
    {
    case WM_CREATE:
        {
            // 创建静态文本标签
            hStatic1 = CreateWindow(
                L"STATIC",                      // 控件类名
                L"请输入您的姓名:",              // 显示文本
                WS_VISIBLE | WS_CHILD,          // 窗口样式
                20, 20,                         // x, y 位置
                150, 25,                        // 宽度, 高度
                hWnd,                           // 父窗口
                (HMENU)ID_STATIC1,              // 控件 ID
                GetModuleHandle(NULL),          // 实例句柄
                NULL);

            // 创建文本输入框
            hEdit1 = CreateWindow(
                L"EDIT",                        // 控件类名
                L"",                            // 初始文本
                WS_VISIBLE | WS_CHILD | WS_BORDER | ES_AUTOHSCROLL,
                20, 50,                         // x, y 位置
                200, 25,                        // 宽度, 高度
                hWnd,                           // 父窗口
                (HMENU)ID_EDIT1,                // 控件 ID
                GetModuleHandle(NULL),
                NULL);

            // 创建多行文本框
            hEdit2 = CreateWindow(
                L"EDIT",
                L"这是一个多行文本框...",
                WS_VISIBLE | WS_CHILD | WS_BORDER | ES_MULTILINE | ES_AUTOVSCROLL | WS_VSCROLL,
                20, 120,                        // x, y 位置
                300, 100,                       // 宽度, 高度
                hWnd,
                (HMENU)ID_EDIT2,
                GetModuleHandle(NULL),
                NULL);

            // 创建按钮1
            hButton1 = CreateWindow(
                L"BUTTON",                      // 控件类名
                L"确定",                        // 按钮文本
                WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                250, 50,                        // x, y 位置
                80, 30,                         // 宽度, 高度
                hWnd,
                (HMENU)ID_BUTTON1,
                GetModuleHandle(NULL),
                NULL);

            // 创建按钮2
            hButton2 = CreateWindow(
                L"BUTTON",
                L"清空",
                WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                340, 50,                        // x, y 位置
                80, 30,
                hWnd,
                (HMENU)ID_BUTTON2,
                GetModuleHandle(NULL),
                NULL);

            // 创建列表框
            hListBox1 = CreateWindow(
                L"LISTBOX",
                NULL,
                WS_VISIBLE | WS_CHILD | WS_BORDER | WS_VSCROLL,
                350, 120,                       // x, y 位置
                150, 100,                       // 宽度, 高度
                hWnd,
                (HMENU)ID_LISTBOX1,
                GetModuleHandle(NULL),
                NULL);

            // 向列表框添加一些项目
            SendMessage(hListBox1, LB_ADDSTRING, 0, (LPARAM)L"选项 1");
            SendMessage(hListBox1, LB_ADDSTRING, 0, (LPARAM)L"选项 2");
            SendMessage(hListBox1, LB_ADDSTRING, 0, (LPARAM)L"选项 3");
        }
        break;

    case WM_COMMAND:
        {
            int wmId = LOWORD(wParam);
            // 分析菜单选择和按钮点击:
            switch (wmId)
            {
            case ID_BUTTON1:  // 确定按钮被点击
                {
                    // 获取文本框内容
                    wchar_t buffer[256];
                    GetWindowText(hEdit1, buffer, 256);

                    // 显示消息框
                    wchar_t message[512];
                    swprintf_s(message, L"您输入的姓名是: %s", buffer);
                    MessageBox(hWnd, message, L"信息", MB_OK | MB_ICONINFORMATION);

                    // 将输入的文本添加到列表框
                    if (wcslen(buffer) > 0)
                    {
                        SendMessage(hListBox1, LB_ADDSTRING, 0, (LPARAM)buffer);
                    }
                }
                break;

            case ID_BUTTON2:  // 清空按钮被点击
                {
                    // 清空文本框
                    SetWindowText(hEdit1, L"");
                    SetWindowText(hEdit2, L"");

                    // 清空列表框
                    SendMessage(hListBox1, LB_RESETCONTENT, 0, 0);

                    MessageBox(hWnd, L"已清空所有内容", L"提示", MB_OK);
                }
                break;

            case ID_LISTBOX1:  // 列表框被选择
                {
                    if (HIWORD(wParam) == LBN_SELCHANGE)
                    {
                        // 获取选中的项目
                        int sel = SendMessage(hListBox1, LB_GETCURSEL, 0, 0);
                        if (sel != LB_ERR)
                        {
                            wchar_t buffer[256];
                            SendMessage(hListBox1, LB_GETTEXT, sel, (LPARAM)buffer);

                            wchar_t message[512];
                            swprintf_s(message, L"您选择了: %s", buffer);
                            SetWindowText(hEdit2, message);
                        }
                    }
                }
                break;

            case IDM_ABOUT:
                DialogBox(hInst, MAKEINTRESOURCE(IDD_ABOUTBOX), hWnd, About);
                break;
            case IDM_EXIT:
                DestroyWindow(hWnd);
                break;
            default:
                return DefWindowProc(hWnd, message, wParam, lParam);
            }
        }
        break;

    case WM_PAINT:
        {
            PAINTSTRUCT ps;
            HDC hdc = BeginPaint(hWnd, &ps);
            // TODO: 在此处添加使用 hdc 的任何绘图代码...
            EndPaint(hWnd, &ps);
        }
        break;

    case WM_DESTROY:
        PostQuitMessage(0);
        break;

    default:
        return DefWindowProc(hWnd, message, wParam, lParam);
    }
    return 0;
}

// "关于"框的消息处理程序。
INT_PTR CALLBACK About(HWND hDlg, UINT message, WPARAM wParam, LPARAM lParam)
{
    UNREFERENCED_PARAMETER(lParam);
    switch (message)
    {
    case WM_INITDIALOG:
        return (INT_PTR)TRUE;

    case WM_COMMAND:
        if (LOWORD(wParam) == IDOK || LOWORD(wParam) == IDCANCEL)
        {
            EndDialog(hDlg, LOWORD(wParam));
            return (INT_PTR)TRUE;
        }
        break;
    }
    return (INT_PTR)FALSE;
}
