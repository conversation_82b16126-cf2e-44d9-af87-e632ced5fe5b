﻿<Window
    x:Class="CSharpCode2CppCode.App.MainWindow"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:avalonedit="http://icsharpcode.net/sharpdevelop/avalonedit"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:CSharpCode2CppCode.App"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    Title="C# to C++ Code Converter"
    Width="1200"
    Height="700"
    mc:Ignorable="d">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <!--  工具栏  -->
        <ToolBar Grid.Row="0">
            <Button
                Name="BtnOpenFile"
                Click="BtnOpenFile_Click"
                Content="打开文件" />
            <Button
                Name="BtnSaveFile"
                Click="BtnSaveFile_Click"
                Content="保存C++文件" />
            <Separator />
            <Button
                Name="BtnConvert"
                Click="BtnConvert_Click"
                Content="转换"
                FontWeight="Bold" />
            <Button
                Name="BtnClear"
                Click="BtnClear_Click"
                Content="清空" />
            <Separator />
            <CheckBox
                Name="ChkIncludeComments"
                Content="保留注释"
                IsChecked="True" />
            <CheckBox
                Name="ChkGenerateHeaders"
                Content="生成头文件"
                IsChecked="True" />
        </ToolBar>

        <!--  主内容区  -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="5" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>

            <!--  左侧 - C# 代码输入  -->
            <GroupBox Grid.Column="0" Header="C# 代码">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="*" />
                    </Grid.RowDefinitions>

                    <StackPanel
                        Grid.Row="0"
                        Margin="5"
                        Orientation="Horizontal">
                        <TextBlock VerticalAlignment="Center" Text="文件名:" />
                        <TextBox
                            Name="TxtCSharpFileName"
                            Width="200"
                            Margin="5,0"
                            Text="Example.cs" />
                    </StackPanel>

                    <avalonedit:TextEditor
                        Name="CSharpEditor"
                        Grid.Row="1"
                        FontFamily="Consolas"
                        FontSize="12"
                        ShowLineNumbers="True"
                        SyntaxHighlighting="C#" />
                </Grid>
            </GroupBox>

            <!--  分隔符  -->
            <GridSplitter
                Grid.Column="1"
                HorizontalAlignment="Stretch"
                Background="Gray" />

            <!--  右侧 - C++ 代码输出  -->
            <GroupBox Grid.Column="2" Header="C++ 代码">
                <TabControl>
                    <TabItem Header="头文件 (.h)">
                        <avalonedit:TextEditor
                            Name="CppHeaderEditor"
                            FontFamily="Consolas"
                            FontSize="12"
                            IsReadOnly="True"
                            ShowLineNumbers="True"
                            SyntaxHighlighting="C++" />
                    </TabItem>
                    <TabItem Header="源文件 (.cpp)">
                        <avalonedit:TextEditor
                            Name="CppSourceEditor"
                            FontFamily="Consolas"
                            FontSize="12"
                            IsReadOnly="True"
                            ShowLineNumbers="True"
                            SyntaxHighlighting="C++" />
                    </TabItem>
                </TabControl>
            </GroupBox>
        </Grid>

        <!--  状态栏  -->
        <StatusBar Grid.Row="2">
            <StatusBarItem>
                <TextBlock Name="StatusText" Text="就绪" />
            </StatusBarItem>
            <StatusBarItem HorizontalAlignment="Right">
                <ProgressBar
                    Name="ProgressBar"
                    Width="200"
                    Height="16"
                    Visibility="Collapsed" />
            </StatusBarItem>
        </StatusBar>
    </Grid>
</Window>
