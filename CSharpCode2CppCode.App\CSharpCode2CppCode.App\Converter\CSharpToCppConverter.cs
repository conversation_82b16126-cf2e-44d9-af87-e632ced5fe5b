﻿using Microsoft.CodeAnalysis;
using Microsoft.CodeAnalysis.CSharp;
using Microsoft.CodeAnalysis.CSharp.Syntax;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace CSharpCode2CppCode.App.Converter
{
    public class CSharpToCppConverter
    {
        private readonly ConversionOptions _options;
        private readonly Dictionary<string, string> _typeMapping;
        private readonly List<string> _includes;
        private readonly List<string> _forwardDeclarations;

        public CSharpToCppConverter(ConversionOptions options = null)
        {
            _options = options ?? new ConversionOptions();
            _typeMapping = InitializeTypeMapping();
            _includes = new List<string>();
            _forwardDeclarations = new List<string>();
        }

        public ConversionResult Convert(string csharpCode, string fileName = "Example")
        {
            try
            {
                var tree = CSharpSyntaxTree.ParseText(csharpCode);
                var root = tree.GetCompilationUnitRoot();

                var headerContent = GenerateHeader(root, fileName);
                var sourceContent = GenerateSource(root, fileName);

                return new ConversionResult
                {
                    Success = true,
                    HeaderContent = headerContent,
                    SourceContent = sourceContent,
                    FileName = fileName
                };
            }
            catch (Exception ex)
            {
                return new ConversionResult
                {
                    Success = false,
                    ErrorMessage = ex.Message
                };
            }
        }

        private string GenerateHeader(CompilationUnitSyntax root, string fileName)
        {
            var sb = new StringBuilder();

            // 头文件保护
            var guardName = $"{fileName.ToUpper()}_H";
            sb.AppendLine($"#ifndef {guardName}");
            sb.AppendLine($"#define {guardName}");
            sb.AppendLine();

            // 包含文件
            AddStandardIncludes(sb);
            sb.AppendLine();

            // 命名空间开始
            var namespaceDecl = root.Members.OfType<NamespaceDeclarationSyntax>().FirstOrDefault();
            if (namespaceDecl != null)
            {
                sb.AppendLine($"namespace {namespaceDecl.Name} {{");
                sb.AppendLine();
            }

            // 类声明
            foreach (var classDecl in GetClasses(root))
            {
                GenerateClassHeader(sb, classDecl);
                sb.AppendLine();
            }

            // 命名空间结束
            if (namespaceDecl != null)
            {
                sb.AppendLine("} // namespace");
                sb.AppendLine();
            }

            sb.AppendLine($"#endif // {guardName}");

            return sb.ToString();
        }

        private string GenerateSource(CompilationUnitSyntax root, string fileName)
        {
            var sb = new StringBuilder();

            // 包含头文件
            sb.AppendLine($"#include \"{fileName}.h\"");
            sb.AppendLine();

            // 命名空间
            var namespaceDecl = root.Members.OfType<NamespaceDeclarationSyntax>().FirstOrDefault();
            if (namespaceDecl != null)
            {
                sb.AppendLine($"namespace {namespaceDecl.Name} {{");
                sb.AppendLine();
            }

            // 类实现
            foreach (var classDecl in GetClasses(root))
            {
                GenerateClassImplementation(sb, classDecl);
                sb.AppendLine();
            }

            // 命名空间结束
            if (namespaceDecl != null)
            {
                sb.AppendLine("} // namespace");
            }

            return sb.ToString();
        }

        private void GenerateClassHeader(StringBuilder sb, ClassDeclarationSyntax classDecl)
        {
            if (_options.IncludeComments)
            {
                sb.AppendLine($"// Class: {classDecl.Identifier.ValueText}");
            }

            // 继承
            var baseList = "";
            if (classDecl.BaseList != null)
            {
                var bases = classDecl.BaseList.Types.Select(t => ConvertType(t.Type.ToString()));
                baseList = " : " + string.Join(", ", bases.Select(b => $"public {b}"));
            }

            sb.AppendLine($"class {classDecl.Identifier.ValueText}{baseList} {{");

            // 访问修饰符分组
            var publicMembers = new List<MemberDeclarationSyntax>();
            var privateMembers = new List<MemberDeclarationSyntax>();
            var protectedMembers = new List<MemberDeclarationSyntax>();

            foreach (var member in classDecl.Members)
            {
                var accessibility = GetAccessibility(member);
                switch (accessibility)
                {
                    case "public":
                        publicMembers.Add(member);
                        break;
                    case "protected":
                        protectedMembers.Add(member);
                        break;
                    default:
                        privateMembers.Add(member);
                        break;
                }
            }

            // Public 成员
            if (publicMembers.Any())
            {
                sb.AppendLine("public:");
                foreach (var member in publicMembers)
                {
                    GenerateMemberHeader(sb, member);
                }
                sb.AppendLine();
            }

            // Protected 成员
            if (protectedMembers.Any())
            {
                sb.AppendLine("protected:");
                foreach (var member in protectedMembers)
                {
                    GenerateMemberHeader(sb, member);
                }
                sb.AppendLine();
            }

            // Private 成员
            if (privateMembers.Any())
            {
                sb.AppendLine("private:");
                foreach (var member in privateMembers)
                {
                    GenerateMemberHeader(sb, member);
                }
            }

            sb.AppendLine("};");
        }

        private void GenerateClassImplementation(StringBuilder sb, ClassDeclarationSyntax classDecl)
        {
            var className = classDecl.Identifier.ValueText;

            foreach (var member in classDecl.Members)
            {
                if (member is MethodDeclarationSyntax method)
                {
                    GenerateMethodImplementation(sb, method, className);
                }
                else if (member is ConstructorDeclarationSyntax constructor)
                {
                    GenerateConstructorImplementation(sb, constructor, className);
                }
            }
        }

        private void GenerateMemberHeader(StringBuilder sb, MemberDeclarationSyntax member)
        {
            switch (member)
            {
                case FieldDeclarationSyntax field:
                    GenerateFieldHeader(sb, field);
                    break;
                case PropertyDeclarationSyntax property:
                    GeneratePropertyHeader(sb, property);
                    break;
                case MethodDeclarationSyntax method:
                    GenerateMethodHeader(sb, method);
                    break;
                case ConstructorDeclarationSyntax constructor:
                    GenerateConstructorHeader(sb, constructor);
                    break;
            }
        }

        private void GenerateFieldHeader(StringBuilder sb, FieldDeclarationSyntax field)
        {
            var type = ConvertType(field.Declaration.Type.ToString());
            foreach (var variable in field.Declaration.Variables)
            {
                sb.AppendLine($"    {type} {variable.Identifier.ValueText};");
            }
        }

        private void GeneratePropertyHeader(StringBuilder sb, PropertyDeclarationSyntax property)
        {
            var type = ConvertType(property.Type.ToString());
            var name = property.Identifier.ValueText;

            // 生成getter
            sb.AppendLine($"    {type} get{name}() const;");

            // 生成setter（如果有set访问器）
            if (property.AccessorList?.Accessors.Any(a => a.Keyword.ValueText == "set") == true)
            {
                sb.AppendLine($"    void set{name}(const {type}& value);");
            }
        }

        private void GenerateMethodHeader(StringBuilder sb, MethodDeclarationSyntax method)
        {
            var returnType = ConvertType(method.ReturnType.ToString());
            var methodName = method.Identifier.ValueText;
            var parameters = GenerateParameterList(method.ParameterList);

            var isStatic = method.Modifiers.Any(m => m.ValueText == "static") ? "static " : "";
            var isVirtual = method.Modifiers.Any(m => m.ValueText == "virtual") ? "virtual " : "";

            sb.AppendLine($"    {isStatic}{isVirtual}{returnType} {methodName}({parameters});");
        }

        private void GenerateConstructorHeader(StringBuilder sb, ConstructorDeclarationSyntax constructor)
        {
            var className = constructor.Identifier.ValueText;
            var parameters = GenerateParameterList(constructor.ParameterList);

            sb.AppendLine($"    {className}({parameters});");
        }

        private void GenerateMethodImplementation(StringBuilder sb, MethodDeclarationSyntax method, string className)
        {
            var returnType = ConvertType(method.ReturnType.ToString());
            var methodName = method.Identifier.ValueText;
            var parameters = GenerateParameterList(method.ParameterList);

            sb.AppendLine($"{returnType} {className}::{methodName}({parameters}) {{");

            if (method.Body != null)
            {
                var bodyCode = ConvertMethodBody(method.Body);
                sb.AppendLine(bodyCode);
            }
            else
            {
                sb.AppendLine("    // TODO: Implement method");
                if (returnType != "void")
                {
                    sb.AppendLine($"    return {GetDefaultValue(returnType)};");
                }
            }

            sb.AppendLine("}");
            sb.AppendLine();
        }

        private void GenerateConstructorImplementation(StringBuilder sb, ConstructorDeclarationSyntax constructor, string className)
        {
            var parameters = GenerateParameterList(constructor.ParameterList);

            sb.AppendLine($"{className}::{className}({parameters}) {{");

            if (constructor.Body != null)
            {
                var bodyCode = ConvertMethodBody(constructor.Body);
                sb.AppendLine(bodyCode);
            }
            else
            {
                sb.AppendLine("    // TODO: Implement constructor");
            }

            sb.AppendLine("}");
            sb.AppendLine();
        }

        private string ConvertMethodBody(BlockSyntax body)
        {
            var sb = new StringBuilder();

            foreach (var statement in body.Statements)
            {
                var convertedStatement = ConvertStatement(statement);
                sb.AppendLine($"    {convertedStatement}");
            }

            return sb.ToString();
        }

        private string ConvertStatement(StatementSyntax statement)
        {
            switch (statement)
            {
                case ReturnStatementSyntax returnStmt:
                    return $"return {ConvertExpression(returnStmt.Expression)};";
                case ExpressionStatementSyntax exprStmt:
                    return $"{ConvertExpression(exprStmt.Expression)};";
                case LocalDeclarationStatementSyntax localDecl:
                    return ConvertLocalDeclaration(localDecl);
                case IfStatementSyntax ifStmt:
                    return ConvertIfStatement(ifStmt);
                case ForStatementSyntax forStmt:
                    return ConvertForStatement(forStmt);
                case WhileStatementSyntax whileStmt:
                    return ConvertWhileStatement(whileStmt);
                default:
                    return $"// TODO: Convert {statement.GetType().Name}";
            }
        }

        private string ConvertExpression(ExpressionSyntax expression)
        {
            if (expression == null) return "";

            switch (expression)
            {
                case LiteralExpressionSyntax literal:
                    return literal.Token.ValueText;
                case IdentifierNameSyntax identifier:
                    return identifier.Identifier.ValueText;
                case MemberAccessExpressionSyntax memberAccess:
                    return $"{ConvertExpression(memberAccess.Expression)}.{memberAccess.Name}";
                case InvocationExpressionSyntax invocation:
                    return ConvertMethodCall(invocation);
                case BinaryExpressionSyntax binary:
                    return $"{ConvertExpression(binary.Left)} {binary.OperatorToken.ValueText} {ConvertExpression(binary.Right)}";
                default:
                    return expression.ToString();
            }
        }

        private string ConvertMethodCall(InvocationExpressionSyntax invocation)
        {
            var method = ConvertExpression(invocation.Expression);
            var args = string.Join(", ", invocation.ArgumentList.Arguments.Select(a => ConvertExpression(a.Expression)));

            // 特殊处理一些C#方法
            if (method == "Console.WriteLine")
            {
                return $"std::cout << {args} << std::endl";
            }

            return $"{method}({args})";
        }

        private string ConvertLocalDeclaration(LocalDeclarationStatementSyntax localDecl)
        {
            var type = ConvertType(localDecl.Declaration.Type.ToString());
            var variables = localDecl.Declaration.Variables.Select(v =>
            {
                var name = v.Identifier.ValueText;
                var initializer = v.Initializer != null ? $" = {ConvertExpression(v.Initializer.Value)}" : "";
                return $"{name}{initializer}";
            });

            return $"{type} {string.Join(", ", variables)};";
        }

        private string ConvertIfStatement(IfStatementSyntax ifStmt)
        {
            var condition = ConvertExpression(ifStmt.Condition);
            var thenStatement = ConvertStatement(ifStmt.Statement);

            var result = $"if ({condition}) {{\n    {thenStatement}\n}}";

            if (ifStmt.Else != null)
            {
                var elseStatement = ConvertStatement(ifStmt.Else.Statement);
                result += $" else {{\n    {elseStatement}\n}}";
            }

            return result;
        }

        // 修复 ConvertForStatement 方法中的类型不匹配问题
        private string ConvertForStatement(ForStatementSyntax forStmt)
        {
            // 将 VariableDeclarationSyntax 转换为 C++ 变量声明字符串
            string declaration = "";
            if (forStmt.Declaration != null)
            {
                var type = ConvertType(forStmt.Declaration.Type.ToString());
                var variables = forStmt.Declaration.Variables.Select(v =>
                {
                    var name = v.Identifier.ValueText;
                    var initializer = v.Initializer != null ? $" = {ConvertExpression(v.Initializer.Value)}" : "";
                    return $"{name}{initializer}";
                });
                declaration = $"{type} {string.Join(", ", variables)}";
            }

            var condition = forStmt.Condition != null ? ConvertExpression(forStmt.Condition) : "";
            var incrementors = string.Join(", ", forStmt.Incrementors.Select(ConvertExpression));

            // 处理循环体
            string body = "// TODO: Convert body";
            if (forStmt.Statement is BlockSyntax block)
            {
                var sbBody = new StringBuilder();
                foreach (var stmt in block.Statements)
                {
                    sbBody.AppendLine($"    {ConvertStatement(stmt)}");
                }
                body = sbBody.ToString().TrimEnd();
            }
            else if (forStmt.Statement != null)
            {
                body = $"    {ConvertStatement(forStmt.Statement)}";
            }

            return $"for ({declaration}; {condition}; {incrementors}) {{\n{body}\n}}";
        }

        private string ConvertWhileStatement(WhileStatementSyntax whileStmt)
        {
            var condition = ConvertExpression(whileStmt.Condition);
            return $"while ({condition}) {{\n    // TODO: Convert body\n}}";
        }

        private string GenerateParameterList(ParameterListSyntax parameterList)
        {
            if (parameterList?.Parameters.Count == 0)
                return "";

            var parameters = parameterList.Parameters.Select(p =>
            {
                var type = ConvertType(p.Type.ToString());
                var name = p.Identifier.ValueText;
                var defaultValue = p.Default != null ? $" = {ConvertExpression(p.Default.Value)}" : "";

                // C++中引用类型参数
                if (IsReferenceType(type))
                {
                    return $"const {type}& {name}{defaultValue}";
                }

                return $"{type} {name}{defaultValue}";
            });

            return string.Join(", ", parameters);
        }

        private string ConvertType(string csharpType)
        {
            // 移除可空类型标记
            csharpType = csharpType.TrimEnd('?');

            if (_typeMapping.ContainsKey(csharpType))
                return _typeMapping[csharpType];

            // 泛型类型处理
            if (csharpType.Contains('<'))
            {
                return ConvertGenericType(csharpType);
            }

            return csharpType; // 保持原样，可能是自定义类型
        }

        private string ConvertGenericType(string genericType)
        {
            if (genericType.StartsWith("List<"))
            {
                var innerType = ExtractGenericArgument(genericType);
                return $"std::vector<{ConvertType(innerType)}>";
            }
            else if (genericType.StartsWith("Dictionary<"))
            {
                var args = ExtractGenericArguments(genericType);
                return $"std::map<{ConvertType(args[0])}, {ConvertType(args[1])}>";
            }

            return genericType;
        }

        private string ExtractGenericArgument(string genericType)
        {
            var start = genericType.IndexOf('<') + 1;
            var end = genericType.LastIndexOf('>');
            return genericType.Substring(start, end - start);
        }

        private string[] ExtractGenericArguments(string genericType)
        {
            var args = ExtractGenericArgument(genericType);
            return args.Split(',').Select(s => s.Trim()).ToArray();
        }

        private bool IsReferenceType(string type)
        {
            var valueTypes = new[] { "int", "float", "double", "bool", "char", "long", "short" };
            return !valueTypes.Contains(type);
        }

        private string GetDefaultValue(string type)
        {
            return type switch
            {
                "int" or "long" or "short" => "0",
                "float" or "double" => "0.0",
                "bool" => "false",
                "char" => "'\\0'",
                "std::string" => "\"\"",
                _ => "nullptr"
            };
        }

        private string GetAccessibility(MemberDeclarationSyntax member)
        {
            if (member.Modifiers.Any(m => m.ValueText == "public"))
                return "public";
            if (member.Modifiers.Any(m => m.ValueText == "protected"))
                return "protected";
            return "private";
        }

        private IEnumerable<ClassDeclarationSyntax> GetClasses(CompilationUnitSyntax root)
        {
            var classes = root.Members.OfType<ClassDeclarationSyntax>();

            var namespaceDecl = root.Members.OfType<NamespaceDeclarationSyntax>().FirstOrDefault();
            if (namespaceDecl != null)
            {
                classes = classes.Concat(namespaceDecl.Members.OfType<ClassDeclarationSyntax>());
            }

            return classes;
        }

        private void AddStandardIncludes(StringBuilder sb)
        {
            sb.AppendLine("#include <iostream>");
            sb.AppendLine("#include <string>");
            sb.AppendLine("#include <vector>");
            sb.AppendLine("#include <map>");
            sb.AppendLine("#include <memory>");
        }

        private Dictionary<string, string> InitializeTypeMapping()
        {
            return new Dictionary<string, string>
            {
                { "int", "int" },
                { "long", "long" },
                { "short", "short" },
                { "byte", "unsigned char" },
                { "float", "float" },
                { "double", "double" },
                { "bool", "bool" },
                { "char", "char" },
                { "string", "std::string" },
                { "String", "std::string" },
                { "void", "void" },
                { "object", "void*" },
                { "Object", "void*" }
            };
        }
    }
  
    public class ConversionOptions
    {
        public bool IncludeComments { get; set; } = true;
        public bool GenerateHeaders { get; set; } = true;
        public bool UseSmartPointers { get; set; } = true;
    }

    public class ConversionResult
    {
        public bool Success { get; set; }
        public string HeaderContent { get; set; }
        public string SourceContent { get; set; }
        public string FileName { get; set; }
        public string ErrorMessage { get; set; }
    }
}
