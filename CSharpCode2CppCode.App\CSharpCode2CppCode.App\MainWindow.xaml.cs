﻿using Microsoft.Win32;
using System;
using System.IO;
using System.Threading.Tasks;
using System.Windows;
using CSharpCode2CppCode.App.Converter;

namespace CSharpCode2CppCode.App
{
    public partial class MainWindow : Window
    {
        private CSharpToCppConverter _converter;

        public MainWindow()
        {
            InitializeComponent();
            InitializeEditor();
            _converter = new CSharpToCppConverter();

            // 添加示例代码
            LoadSampleCode();
        }

        private void InitializeEditor()
        {
            // 设置编辑器属性
            CSharpEditor.Options.EnableHyperlinks = false;
            CSharpEditor.Options.EnableEmailHyperlinks = false;

            CppHeaderEditor.Options.EnableHyperlinks = false;
            CppSourceEditor.Options.EnableHyperlinks = false;
        }

        private void LoadSampleCode()
        {
            var sampleCode = @"using System;
using System.Collections.Generic;

namespace SampleNamespace
{
    public class Calculator
    {
        private int _result;
        
        public int Result 
        { 
            get { return _result; } 
            set { _result = value; } 
        }

        public Calculator()
        {
            _result = 0;
        }

        public Calculator(int initialValue)
        {
            _result = initialValue;
        }

        public int Add(int a, int b)
        {
            _result = a + b;
            return _result;
        }

        public int Subtract(int a, int b)
        {
            _result = a - b;
            return _result;
        }

        public void PrintResult()
        {
            Console.WriteLine(""Result: "" + _result);
        }

        public static int Multiply(int a, int b)
        {
            return a * b;
        }

        public virtual bool IsPositive()
        {
            return _result > 0;
        }
    }

    public class ScientificCalculator : Calculator
    {
        public double Power(double baseValue, double exponent)
        {
            return Math.Pow(baseValue, exponent);
        }

        public override bool IsPositive()
        {
            return base.IsPositive();
        }
    }
}";
            CSharpEditor.Text = sampleCode;
        }

        private async void BtnConvert_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                StatusText.Text = "正在转换...";
                ProgressBar.Visibility = Visibility.Visible;
                ProgressBar.IsIndeterminate = true;

                var options = new ConversionOptions
                {
                    IncludeComments = ChkIncludeComments.IsChecked == true,
                    GenerateHeaders = ChkGenerateHeaders.IsChecked == true
                };

                _converter = new CSharpToCppConverter(options);

                var fileName = Path.GetFileNameWithoutExtension(TxtCSharpFileName.Text);
                var csharpCode = CSharpEditor.Text; // 在UI线程中获取文本内容

                // 在后台线程中执行转换
                var result = await Task.Run(() => _converter.Convert(csharpCode, fileName));

                // 回到UI线程更新界面
                if (result.Success)
                {
                    CppHeaderEditor.Text = result.HeaderContent;
                    CppSourceEditor.Text = result.SourceContent;
                    StatusText.Text = "转换完成";
                }
                else
                {
                    MessageBox.Show($"转换失败: {result.ErrorMessage}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    StatusText.Text = "转换失败";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"转换过程中发生错误: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                StatusText.Text = "转换失败";
            }
            finally
            {
                ProgressBar.Visibility = Visibility.Collapsed;
                ProgressBar.IsIndeterminate = false;
            }
        }

        private void BtnOpenFile_Click(object sender, RoutedEventArgs e)
        {
            var dialog = new OpenFileDialog
            {
                Title = "选择C#文件",
                Filter = "C# 文件|*.cs|所有文件|*.*",
                DefaultExt = "cs"
            };

            if (dialog.ShowDialog() == true)
            {
                try
                {
                    var content = File.ReadAllText(dialog.FileName);
                    CSharpEditor.Text = content;
                    TxtCSharpFileName.Text = Path.GetFileName(dialog.FileName);
                    StatusText.Text = $"已加载文件: {dialog.FileName}";
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"读取文件失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void BtnSaveFile_Click(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrWhiteSpace(CppHeaderEditor.Text) && string.IsNullOrWhiteSpace(CppSourceEditor.Text))
            {
                MessageBox.Show("没有可保存的C++代码，请先进行转换。", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            var dialog = new SaveFileDialog
            {
                Title = "保存C++文件",
                Filter = "C++ 头文件|*.h|C++ 源文件|*.cpp|所有文件|*.*",
                DefaultExt = "h"
            };

            var fileName = Path.GetFileNameWithoutExtension(TxtCSharpFileName.Text);
            dialog.FileName = fileName;

            if (dialog.ShowDialog() == true)
            {
                try
                {
                    var selectedPath = dialog.FileName;
                    var directory = Path.GetDirectoryName(selectedPath);
                    var baseFileName = Path.GetFileNameWithoutExtension(selectedPath);

                    // 保存头文件
                    if (!string.IsNullOrWhiteSpace(CppHeaderEditor.Text))
                    {
                        var headerPath = Path.Combine(directory, baseFileName + ".h");
                        File.WriteAllText(headerPath, CppHeaderEditor.Text);
                    }

                    // 保存源文件
                    if (!string.IsNullOrWhiteSpace(CppSourceEditor.Text))
                    {
                        var sourcePath = Path.Combine(directory, baseFileName + ".cpp");
                        File.WriteAllText(sourcePath, CppSourceEditor.Text);
                    }

                    StatusText.Text = $"文件已保存到: {directory}";
                    MessageBox.Show("C++文件保存成功！", "成功", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"保存文件失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void BtnClear_Click(object sender, RoutedEventArgs e)
        {
            CSharpEditor.Clear();
            CppHeaderEditor.Clear();
            CppSourceEditor.Clear();
            TxtCSharpFileName.Text = "Example.cs";
            StatusText.Text = "已清空";
        }
    }
}