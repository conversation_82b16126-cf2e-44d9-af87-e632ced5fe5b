// 示例：在 Windows 桌面应用程序中添加控件的代码

#include "framework.h"
#include "MyDesktopApp.h"
#include <windows.h>
#include <commctrl.h>

// 控件 ID 定义
#define ID_BUTTON1      1001
#define ID_BUTTON2      1002
#define ID_EDIT1        1003
#define ID_EDIT2        1004
#define ID_STATIC1      1005
#define ID_LISTBOX1     1006

// 全局变量
HWND hButton1, hButton2;
HWND hEdit1, hEdit2;
HWND hStatic1;
HWND hListBox1;

LRESULT CALLBACK WndProc(HWND hWnd, UINT message, WPARAM wParam, LPARAM lParam)
{
    switch (message)
    {
    case WM_CREATE:
        {
            // 创建静态文本标签
            hStatic1 = CreateWindow(
                L"STATIC",                      // 控件类名
                L"请输入您的姓名:",              // 显示文本
                WS_VISIBLE | WS_CHILD,          // 窗口样式
                20, 20,                         // x, y 位置
                150, 25,                        // 宽度, 高度
                hWnd,                           // 父窗口
                (HMENU)ID_STATIC1,              // 控件 ID
                GetModuleHandle(NULL),          // 实例句柄
                NULL);

            // 创建文本输入框
            hEdit1 = CreateWindow(
                L"EDIT",                        // 控件类名
                L"",                            // 初始文本
                WS_VISIBLE | WS_CHILD | WS_BORDER | ES_AUTOHSCROLL,
                20, 50,                         // x, y 位置
                200, 25,                        // 宽度, 高度
                hWnd,                           // 父窗口
                (HMENU)ID_EDIT1,                // 控件 ID
                GetModuleHandle(NULL),
                NULL);

            // 创建多行文本框
            hEdit2 = CreateWindow(
                L"EDIT",
                L"这是一个多行文本框...",
                WS_VISIBLE | WS_CHILD | WS_BORDER | ES_MULTILINE | ES_AUTOVSCROLL | WS_VSCROLL,
                20, 120,                        // x, y 位置
                300, 100,                       // 宽度, 高度
                hWnd,
                (HMENU)ID_EDIT2,
                GetModuleHandle(NULL),
                NULL);

            // 创建按钮1
            hButton1 = CreateWindow(
                L"BUTTON",                      // 控件类名
                L"确定",                        // 按钮文本
                WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                250, 50,                        // x, y 位置
                80, 30,                         // 宽度, 高度
                hWnd,
                (HMENU)ID_BUTTON1,
                GetModuleHandle(NULL),
                NULL);

            // 创建按钮2
            hButton2 = CreateWindow(
                L"BUTTON",
                L"清空",
                WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                340, 50,                        // x, y 位置
                80, 30,
                hWnd,
                (HMENU)ID_BUTTON2,
                GetModuleHandle(NULL),
                NULL);

            // 创建列表框
            hListBox1 = CreateWindow(
                L"LISTBOX",
                NULL,
                WS_VISIBLE | WS_CHILD | WS_BORDER | WS_VSCROLL,
                350, 120,                       // x, y 位置
                150, 100,                       // 宽度, 高度
                hWnd,
                (HMENU)ID_LISTBOX1,
                GetModuleHandle(NULL),
                NULL);

            // 向列表框添加一些项目
            SendMessage(hListBox1, LB_ADDSTRING, 0, (LPARAM)L"选项 1");
            SendMessage(hListBox1, LB_ADDSTRING, 0, (LPARAM)L"选项 2");
            SendMessage(hListBox1, LB_ADDSTRING, 0, (LPARAM)L"选项 3");
        }
        break;

    case WM_COMMAND:
        {
            int wmId = LOWORD(wParam);
            switch (wmId)
            {
            case ID_BUTTON1:  // 确定按钮被点击
                {
                    // 获取文本框内容
                    wchar_t buffer[256];
                    GetWindowText(hEdit1, buffer, 256);
                    
                    // 显示消息框
                    wchar_t message[512];
                    swprintf_s(message, L"您输入的姓名是: %s", buffer);
                    MessageBox(hWnd, message, L"信息", MB_OK | MB_ICONINFORMATION);
                    
                    // 将输入的文本添加到列表框
                    if (wcslen(buffer) > 0)
                    {
                        SendMessage(hListBox1, LB_ADDSTRING, 0, (LPARAM)buffer);
                    }
                }
                break;

            case ID_BUTTON2:  // 清空按钮被点击
                {
                    // 清空文本框
                    SetWindowText(hEdit1, L"");
                    SetWindowText(hEdit2, L"");
                    
                    // 清空列表框
                    SendMessage(hListBox1, LB_RESETCONTENT, 0, 0);
                    
                    MessageBox(hWnd, L"已清空所有内容", L"提示", MB_OK);
                }
                break;

            case ID_LISTBOX1:  // 列表框被选择
                {
                    if (HIWORD(wParam) == LBN_SELCHANGE)
                    {
                        // 获取选中的项目
                        int sel = SendMessage(hListBox1, LB_GETCURSEL, 0, 0);
                        if (sel != LB_ERR)
                        {
                            wchar_t buffer[256];
                            SendMessage(hListBox1, LB_GETTEXT, sel, (LPARAM)buffer);
                            
                            wchar_t message[512];
                            swprintf_s(message, L"您选择了: %s", buffer);
                            SetWindowText(hEdit2, message);
                        }
                    }
                }
                break;
            }
        }
        break;

    case WM_PAINT:
        {
            PAINTSTRUCT ps;
            HDC hdc = BeginPaint(hWnd, &ps);
            // 在这里可以添加自定义绘制代码
            EndPaint(hWnd, &ps);
        }
        break;

    case WM_DESTROY:
        PostQuitMessage(0);
        break;

    default:
        return DefWindowProc(hWnd, message, wParam, lParam);
    }
    return 0;
}
